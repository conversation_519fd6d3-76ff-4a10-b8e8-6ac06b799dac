package com.ruoyi.custom.admin.assessment.domain;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 提醒通知项
 *
 * <AUTHOR>
 * @date 2025-08-01
 */
@Data
@ApiModel(value = "提醒通知项")
public class RemindNoticeItem {

    @ApiModelProperty(value = "提醒消息内容")
    private String message;

    @ApiModelProperty(value = "模版类型：1-模版一（明天开始评估），2-模版二（已超时评估）")
    private String type;

    public RemindNoticeItem() {
    }

    public RemindNoticeItem(String message, String type) {
        this.message = message;
        this.type = type;
    }
}
