<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.custom.admin.assessment.mapper.AssessmentPlanMapper">

    <resultMap type="AssessmentPlan" id="AssessmentPlanResult">
        <result property="id" column="id"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="customerId" column="customer_id"/>
        <result property="assessorId" column="assessor_id"/>
        <result property="assessorName" column="assessor_name"/>
        <result property="templateId" column="template_id"/>
        <result property="indicator" column="indicator"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
        <result property="grade" column="grade"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="assessmentReason" column="assessment_reason"/>
        <result property="assessmentLocation" column="assessment_location"/>
        <result property="plannedStartDate" column="planned_start_date"/>
        <result property="actualStartTime" column="actual_start_time"/>
        <result property="actualEndTime" column="actual_end_time"/>
        <result property="status" column="status"/>
        <result property="assessmentResult" column="assessment_result"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler"/>
        <result property="nursingPlanIndicators" column="nursing_plan_indicators"
                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>
        <result property="remarks" column="remarks"/>
        <result property="attachmentUrl" column="attachment_url"/>

        <result property="elderlyName" column="elderly_name"/>
        <result property="elderlyAge" column="elderly_age"/>
        <result property="elderlyIdCardNum" column="elderly_id_card_num"/>
        <result property="elderlySex" column="elderly_sex"/>
        <result property="elderlyDateBirth" column="elderly_date_birth"/>
        <result property="elderlyMaritalStatus" column="elderly_marital_status"/>
        <result property="elderlyNation" column="elderly_nation"/>

        <result property="templateName" column="template_name"/>
    </resultMap>

    <sql id="selectAssessmentPlanVo">
        SELECT t.id,
               t.serial_number,
               t.customer_id,
               t.assessor_id,
               t.assessor_name,
               t.template_id,
               t.indicator,
               t.grade,
               t.assessment_reason,
               t.assessment_location,
               t.planned_start_date,
               t.actual_start_time,
               t.actual_end_time,
               t.status,
               t.assessment_result,
               t.nursing_plan_indicators,
               t.remarks,
               t.attachment_url,

               t1.elder_name        AS elderly_name,
               t1.elder_age         AS elderly_age,
               t1.id_card_number AS elderly_id_card_num,
               t1.elder_gender         AS elderly_sex,
               t1.elder_birthday AS elderly_date_birth,
               t1.marital_status AS elderly_marital_status,
               t1.nation AS elderly_nation,

               t2.name        AS template_name

        FROM t_assessment_plan t
                 LEFT JOIN t_marketing_customer_info t1 ON t.customer_id = t1.id
                 LEFT JOIN t_assessment_template t2 ON t.template_id = t2.id
    </sql>

    <select id="selectAssessmentPlanList" parameterType="AssessmentPlan" resultMap="AssessmentPlanResult">
        <include refid="selectAssessmentPlanVo"/>
        <where>
            <if test="serialNumber != null  and serialNumber != ''">
                and t.serial_number = #{serialNumber}
            </if>
            <if test="customerId != null">
                and t.customer_id = #{customerId}
            </if>
            <if test="elderlyName != null  and elderlyName != ''">
                and t1.elder_Name like concat('%', #{elderlyName}, '%')
            </if>
            <if test="assessorId != null ">
                and t.assessor_id = #{assessorId}
            </if>
            <if test="assessorName != null  and assessorName != ''">
                and t.assessor_name like concat('%', #{assessorName}, '%')
            </if>
            <if test="templateId != null ">
                and t.template_id = #{templateId}
            </if>
            <if test="assessmentReason != null  and assessmentReason != ''">
                and t.assessment_reason = #{assessmentReason}
            </if>
            <if test="assessmentLocation != null  and assessmentLocation != ''">
                and t.assessment_location = #{assessmentLocation}
            </if>
            <if test="plannedStartDate != null ">
                and date_format(t.planned_start_date,'%y%m%d') = date_format(#{plannedStartDate},'%y%m%d')
            </if>
            <if test="actualStartTime != null ">
                and t.actual_start_time = #{actualStartTime}
            </if>
            <if test="actualEndTime != null ">
                and t.actual_end_time = #{actualEndTime}
            </if>
            <if test="status != null  and status != ''">
                and t.status = #{status}
            </if>
            <if test="assessmentResult != null  and assessmentResult != ''">
                and t.assessment_result = #{assessmentResult}
            </if>
            <if test="remarks != null  and remarks != ''">
                and t.remarks = #{remarks}
            </if>

            <if test="params.beginPlannedStartDate != null">
                and date_format(#{params.beginPlannedStartDate},'%y-%m-%d') >= t.planned_start_date
            </if>

            <if test="params.beginPlannedStartDate != null and params.endPlannedStartDate!=null">and
                t.planned_start_date BETWEEN
                date_format(#{params.beginPlannedStartDate},'%y-%m-%d') and
                date_format(#{params.endPlannedStartDate},'%y-%m-%d')
            </if>
        </where>
        ORDER BY t.id DESC
    </select>

    <select id="selectAssessmentPlanById" parameterType="Long"
            resultMap="AssessmentPlanResult">
        <include refid="selectAssessmentPlanVo"/>
        where t.id = #{id}
    </select>

    <!--    <select id="getAnswer" resultMap="indicatorMap">-->
    <!--        SELECT indicator-->
    <!--        FROM t_assessment_plan-->
    <!--        WHERE id = #{id}-->
    <!--    </select>-->

    <!--    <resultMap id="indicatorMap" type="AssessmentPlan">-->
    <!--        <result property="indicator" column="indicator"-->
    <!--                typeHandler="com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler"/>-->
    <!--    </resultMap>-->

    <insert id="insertAssessmentPlan" parameterType="AssessmentPlan" useGeneratedKeys="true"
            keyProperty="id">
        insert into t_assessment_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number,
            </if>
            <if test="customerId != null">customer_id,
            </if>
            <if test="assessorId != null">assessor_id,
            </if>
            <if test="assessorName != null and assessorName != ''">assessor_name,
            </if>
            <if test="templateId != null">template_id,
            </if>
            <if test="indicator != null">indicator,
            </if>
            <if test="grade != null">grade,
            </if>
            <if test="assessmentReason != null and assessmentReason != ''">assessment_reason,
            </if>
            <if test="assessmentLocation != null and assessmentLocation != ''">assessment_location,
            </if>
            <if test="plannedStartDate != null">planned_start_date,
            </if>
            <if test="actualStartTime != null">actual_start_time,
            </if>
            <if test="actualEndTime != null">actual_end_time,
            </if>
            <if test="status != null and status != ''">status,
            </if>
            <if test="assessmentResult != null">assessment_result,
            </if>
            <if test="nursingPlanIndicators != null">nursing_plan_indicators,
            </if>
            <if test="remarks != null">remarks,
            </if>
            <if test="attachmentUrl != null and attachmentUrl != ''">attachment_url,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">#{serialNumber},
            </if>
            <if test="customerId != null">#{customerId},
            </if>
            <if test="assessorId != null">#{assessorId},
            </if>
            <if test="assessorName != null and assessorName != ''">#{assessorName},
            </if>
            <if test="templateId != null">#{templateId},
            </if>
            <if test="indicator != null">
                #{indicator ,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
            <if test="grade != null">
                #{grade ,jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="assessmentReason != null and assessmentReason != ''">#{assessmentReason},
            </if>
            <if test="assessmentLocation != null and assessmentLocation != ''">#{assessmentLocation},
            </if>
            <if test="plannedStartDate != null">#{plannedStartDate},
            </if>
            <if test="actualStartTime != null">#{actualStartTime},
            </if>
            <if test="actualEndTime != null">#{actualEndTime},
            </if>
            <if test="status != null and status != ''">#{status},
            </if>
            <if test="assessmentResult != null">
                #{assessmentResult, jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="nursingPlanIndicators != null">
                #{nursingPlanIndicators, jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
            <if test="remarks != null">#{remarks},
            </if>
            <if test="attachmentUrl != null and attachmentUrl != ''">#{attachmentUrl},
            </if>
        </trim>
    </insert>

    <update id="updateAssessmentPlan" parameterType="AssessmentPlan">
        update t_assessment_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="serialNumber != null and serialNumber != ''">serial_number =
                #{serialNumber},
            </if>
            <if test="customerId != null">customer_id =
                #{customerId},
            </if>
            <if test="assessorId != null">assessor_id =
                #{assessorId},
            </if>
            <if test="assessorName != null and assessorName != ''">assessor_name =
                #{assessorName},
            </if>
            <if test="templateId != null">template_id =
                #{templateId},
            </if>
            <if test="indicator != null">indicator =
                #{indicator, jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
            <if test="grade != null">grade =
                #{grade, jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="assessmentReason != null and assessmentReason != ''">assessment_reason =
                #{assessmentReason},
            </if>
            <if test="assessmentLocation != null and assessmentLocation != ''">assessment_location =
                #{assessmentLocation},
            </if>
            <if test="plannedStartDate != null">planned_start_date =
                #{plannedStartDate},
            </if>
            <if test="actualStartTime != null">actual_start_time =
                #{actualStartTime},
            </if>
            <if test="actualEndTime != null">actual_end_time =
                #{actualEndTime},
            </if>
            <if test="status != null and status != ''">status =
                #{status},
            </if>
            <if test="assessmentResult != null">assessment_result =
                #{assessmentResult, jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonObjectTypeHandler},
            </if>
            <if test="nursingPlanIndicators != null">nursing_plan_indicators =
                #{nursingPlanIndicators, jdbcType=OTHER,typeHandler=com.ruoyi.custom.config.mybatis.handler.JsonArrayTypeHandler},
            </if>
            <if test="remarks != null">remarks =
                #{remarks},
            </if>
            <if test="attachmentUrl != null and attachmentUrl != ''">attachment_url =
                #{attachmentUrl},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssessmentPlanById" parameterType="Long">
        DELETE
        FROM t_assessment_plan
        WHERE id = #{id}
    </delete>

    <delete id="deleteAssessmentPlanByIds" parameterType="String">
        delete from t_assessment_plan where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getCalendarList" resultType="java.lang.String">
        SELECT DATE_FORMAT(planned_start_date, '%Y-%m-%d') AS calendar
        FROM t_assessment_plan
        WHERE planned_start_date BETWEEN DATE_FORMAT(#{month}, '%Y-%m-01') AND LAST_DAY(#{month})
        AND status != 2
    </select>

    <update id="saveAssessmentResult">
        UPDATE t_assessment_plan
        SET assessment_result = JSON_SET(
                assessment_result,
                '$.suggestion', #{suggestion})
        WHERE id = #{id}
    </update>

    <select id="selectAssessmentPlanCount" parameterType="AssessmentPlan" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM t_assessment_plan
        WHERE 1 = 1
        <if test="customerId != null">AND customer_id = #{customerId}
        </if>
        <if test="assessorId != null">AND assessor_id = #{assessorId}
        </if>
        <if test="assessorName != null and assessorName != ''">AND assessor_name = #{assessorName}
        </if>
        <if test="templateId != null">AND template_id = #{templateId}
        </if>
        <if test="assessmentReason != null and assessmentReason != ''">AND assessment_reason = #{assessmentReason}
        </if>
        <if test="assessmentLocation != null and assessmentLocation != ''">AND assessment_location =
            #{assessmentLocation}
        </if>
        <if test="plannedStartDate != null">AND DATE_FORMAT(planned_start_date, '%Y-%m-%d') =
            DATE_FORMAT(#{plannedStartDate}, '%Y-%m-%d')
        </if>
        <if test="status != null and status != ''">AND status = #{status}</if>
    </select>

</mapper>
